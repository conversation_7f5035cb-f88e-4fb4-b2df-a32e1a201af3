"""GitLab File SHA Tool - Get SHA hash of a file at a specific commit reference.

This tool retrieves the SHA hash of a file from a GitLab repository at a specific
commit reference (branch, tag, or commit hash).
"""

import j<PERSON>
from typing import Any, Dict
from base_tool import BaseTool
from tools import make_gitlab_tools, GitLabAPIError


class GetFileSHATool(BaseTool):
    """Tool for getting SHA hash of a file at a specific commit reference."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the GitLab File SHA tool.

        Args:
            config (dict): Configuration containing GitLab settings:
                - gitlab_url: GitLab instance URL (e.g., 'https://gitlab.com')
                - project_id: GitLab project ID
                - token: GitLab access token
        """
        super().__init__(config)

        # Allow None config for registry registration
        if self.config is None:
            self.get_sha_func = None
            return

        if not self.config or len(self.config) == 0:
            raise ValueError("GitLab configuration is required")

        required_keys = ['gitlab_url', 'project_id', 'token']
        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"Missing required configuration key: {key}")

        # Initialize GitLab tools
        self.get_sha_func, _, _ = make_gitlab_tools(self.config)

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "get_file_sha"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Get SHA hash of a file at a specific commit reference. "
                "Input should contain 'commit_ref' and 'file_path' keys.")

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool to get file SHA hash.

        Args:
            input_data (dict): Input parameters containing:
                - commit_ref (str): Commit reference (branch, tag, or commit hash)
                - file_path (str): Path to the file in the repository

        Returns:
            dict: Tool execution results containing:
                - success (bool): Whether the operation succeeded
                - sha (str): SHA hash of the file (if successful)
                - error (str): Error message (if failed)
        """
        try:
            # Check if tool is properly configured
            if self.get_sha_func is None:
                return {
                    'success': False,
                    'error': "Tool not configured. Please provide GitLab configuration."
                }

            # Validate input
            if 'commit_ref' not in input_data:
                return {
                    'success': False,
                    'error': "Missing required parameter: 'commit_ref'"
                }

            if 'file_path' not in input_data:
                return {
                    'success': False,
                    'error': "Missing required parameter: 'file_path'"
                }

            commit_ref = input_data.get('commit_ref', 'main')
            file_path = input_data['file_path']

            # Get SHA hash using GitLab API
            sha = self.get_sha_func(commit_ref, file_path)
            
            return {
                'success': True,
                'sha': sha,
                'commit_ref': commit_ref,
                'file_path': file_path
            }
            
        except GitLabAPIError as e:
            return {
                'success': False,
                'error': f"GitLab API Error: {str(e)}"
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"Unexpected error: {str(e)}"
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Execute the tool from a JSON string input (for LangChain compatibility).

        Args:
            input_str (str): JSON string with 'commit_ref' and 'file_path' keys
                Example: '{"commit_ref": "main", "file_path": "README.md"}'

        Returns:
            str: SHA hash or error message
        """
        try:
            params = json.loads(input_str)
            result = self.run(params)
            
            if result['success']:
                return result['sha']
            else:
                return result['error']
                
        except json.JSONDecodeError as e:
            return f"Error parsing input: {str(e)}. Expected JSON with 'commit_ref' and 'file_path' keys."
        except Exception as e:
            return f"Error: {str(e)}"
