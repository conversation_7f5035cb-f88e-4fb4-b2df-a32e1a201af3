"""SQL Database Query Checker Tool - Validate SQL queries.

This tool validates SQL queries for syntax errors and common mistakes
before execution to prevent database errors.
"""

import json
from typing import Any, Dict
from base_tool import BaseTool
from langchain_community.utilities import SQLDatabase
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit


class SQLDatabaseQueryCheckerTool(BaseTool):
    """Tool for validating SQL queries."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the SQL Database Query Checker tool.

        Args:
            config (dict): Configuration containing:
                - db (SQLDatabase): LangChain SQLDatabase instance
                - llm: Language model for SQL operations
        """
        super().__init__(config)
        
        if not self.config:
            raise ValueError("Configuration is required")
        
        if 'db' not in self.config:
            raise ValueError("Database instance 'db' is required in configuration")
        
        self.db = self.config['db']
        self.llm = self.config.get('llm')
        
        # Initialize SQL toolkit to get the query checker tool
        if self.llm:
            toolkit = SQLDatabaseToolkit(db=self.db, llm=self.llm)
            sql_tools = toolkit.get_tools()
            
            # Find the query checker tool
            self.query_checker_tool = None
            for tool in sql_tools:
                if tool.name == "sql_db_query_checker":
                    self.query_checker_tool = tool
                    break
            
            if not self.query_checker_tool:
                raise ValueError("Could not find sql_db_query_checker tool in toolkit")
        else:
            raise ValueError("Language model 'llm' is required in configuration")

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "sql_db_query_checker"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Validate SQL queries for syntax errors and common mistakes. "
                "Input should contain 'query' key with the SQL query string.")

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool to validate SQL query.

        Args:
            input_data (dict): Input parameters containing:
                - query (str): SQL query to validate

        Returns:
            dict: Tool execution results containing:
                - success (bool): Whether the operation succeeded
                - result (str): Validation result (if successful)
                - error (str): Error message (if failed)
        """
        try:
            if 'query' not in input_data:
                return {
                    'success': False,
                    'error': "Missing required parameter: 'query'"
                }
            
            query = input_data['query']
            
            # Remove outer quotes but preserve inner quotes in SQL
            query = query.strip()
            if (query.startswith('"') and query.endswith('"')) or \
               (query.startswith("'") and query.endswith("'")):
                query = query[1:-1]
            
            if not query:
                return {
                    'success': False,
                    'error': "Query cannot be empty"
                }
            
            # Validate query using the SQL toolkit tool
            result = self.query_checker_tool._run(query)
            
            return {
                'success': True,
                'result': result,
                'query': query
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Error checking query: {str(e)}"
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Execute the tool from a string input (for LangChain compatibility).

        Args:
            input_str (str): SQL query string to validate

        Returns:
            str: Validation result or error message
        """
        try:
            # Treat input as SQL query string
            result = self.run({'query': input_str})
            
            if result['success']:
                return result['result']
            else:
                return result['error']
                
        except Exception as e:
            return f"Error: {str(e)}"
