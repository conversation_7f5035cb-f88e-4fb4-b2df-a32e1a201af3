"""Get Log Content Tool - Get content from a log file.

This tool reads content from log files with options to limit the number of lines
and choose between reading from the beginning (head) or end (tail) of the file.
"""

import json
from typing import Any, Dict
from base_tool import BaseTool
from file_tools import get_log_file_content, FileSystemError


class GetLogContentTool(BaseTool):
    """Tool for getting content from a log file."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the Get Log Content tool.

        Args:
            config (dict): Configuration containing:
                - log_folder (str): Path to log files folder
                - max_log_lines (int, optional): Default maximum lines to read
        """
        super().__init__(config)
        
        if not self.config or 'log_folder' not in self.config:
            raise ValueError("Configuration with 'log_folder' is required")
        
        self.log_folder = self.config['log_folder']
        self.default_max_lines = self.config.get('max_log_lines', 1000)

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "get_log_content"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("Get content from a log file. Input should contain 'filename' and "
                "optional 'max_lines', 'tail' keys, or just the filename.")

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool to get log file content.

        Args:
            input_data (dict): Input parameters containing:
                - filename (str): Name of the log file
                - max_lines (int, optional): Maximum lines to read (default: 1000)
                - tail (bool, optional): Read from end of file (default: True)

        Returns:
            dict: Tool execution results containing:
                - success (bool): Whether the operation succeeded
                - content (dict): Log file content information (if successful)
                - error (str): Error message (if failed)
        """
        try:
            if 'filename' not in input_data:
                return {
                    'success': False,
                    'error': "Missing required parameter: 'filename'"
                }
            
            filename = input_data['filename']
            max_lines = input_data.get('max_lines', self.default_max_lines)
            tail = input_data.get('tail', True)
            
            # Remove quotes that might be added by LLM
            filename = filename.strip().strip("'\"")
            
            if not filename:
                return {
                    'success': False,
                    'error': "Filename cannot be empty"
                }
            
            # Get log file content
            content = get_log_file_content(filename, self.log_folder, max_lines, tail)
            
            return {
                'success': True,
                'content': content,
                'filename': filename,
                'max_lines': max_lines,
                'tail': tail,
                'folder': self.log_folder
            }
            
        except FileSystemError as e:
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"Unexpected error: {str(e)}"
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Execute the tool from a string input (for LangChain compatibility).

        Args:
            input_str (str): JSON string with parameters or just filename

        Returns:
            str: JSON string with log content or error message
        """
        try:
            # Try to parse as JSON first
            if input_str.startswith('{'):
                params = json.loads(input_str)
                result = self.run(params)
            else:
                # Treat as filename string
                result = self.run({'filename': input_str})
            
            if result['success']:
                return json.dumps(result['content'], indent=2)
            else:
                return f"Error getting log content: {result['error']}"
                
        except json.JSONDecodeError as e:
            return f"Error parsing input: {str(e)}. Expected JSON with 'filename' key or just filename string."
        except Exception as e:
            return f"Error: {str(e)}"
