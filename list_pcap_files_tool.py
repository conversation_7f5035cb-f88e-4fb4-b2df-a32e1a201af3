"""List PCAP Files Tool - List PCAP files in configured folder.

This tool lists PCAP files (*.pcap, *.pcapng, *.cap) in the configured folder
with detailed metadata including size, timestamps, and file information.
"""

import json
from typing import Any, Dict, List
from base_tool import BaseTool
from file_tools import list_pcap_files, FileSystemError


class ListPCAPFilesTool(BaseTool):
    """Tool for listing PCAP files in the configured folder."""

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the List PCAP Files tool.

        Args:
            config (dict): Configuration containing:
                - pcap_folder (str): Path to PCAP files folder
        """
        super().__init__(config)

        # Allow None config for registry registration
        if self.config is None:
            self.pcap_folder = None
            return

        if not self.config or 'pcap_folder' not in self.config:
            raise ValueError("Configuration with 'pcap_folder' is required")

        self.pcap_folder = self.config['pcap_folder']

    @property
    def name(self) -> str:
        """Returns the unique name of the tool."""
        return "list_pcap_files"

    @property
    def description(self) -> str:
        """Returns a description of what the tool does."""
        return ("List PCAP files in the configured folder. "
                "Input should contain 'pattern' key (e.g., '*.pcap', '*.pcapng').")

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool to list PCAP files.

        Args:
            input_data (dict): Input parameters containing:
                - pattern (str, optional): File pattern to match (default: "*.pcap")

        Returns:
            dict: Tool execution results containing:
                - success (bool): Whether the operation succeeded
                - files (list): List of PCAP file information (if successful)
                - count (int): Number of files found
                - error (str): Error message (if failed)
        """
        try:
            pattern = input_data.get('pattern', '*.pcap')
            
            # Remove quotes that might be added by LLM
            pattern = pattern.strip().strip("'\"") if pattern.strip() else "*.pcap"
            
            # List PCAP files
            files = list_pcap_files(self.pcap_folder, pattern)
            
            return {
                'success': True,
                'files': files,
                'count': len(files),
                'pattern': pattern,
                'folder': self.pcap_folder
            }
            
        except FileSystemError as e:
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"Unexpected error: {str(e)}"
            }

    def run_from_string(self, input_str: str) -> str:
        """
        Execute the tool from a string input (for LangChain compatibility).

        Args:
            input_str (str): File pattern (e.g., "*.pcap", "*.pcapng")

        Returns:
            str: JSON string with file list or error message
        """
        try:
            # Treat input as pattern string
            result = self.run({'pattern': input_str})
            
            if result['success']:
                return json.dumps(result['files'], indent=2)
            else:
                return f"Error listing PCAP files: {result['error']}"
                
        except Exception as e:
            return f"Error: {str(e)}"
